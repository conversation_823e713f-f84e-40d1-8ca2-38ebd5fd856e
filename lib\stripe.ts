import { loadStripe } from '@stripe/stripe-js';

let stripePromise: Promise<any> | null = null;

const getStripe = () => {
  if (!stripePromise && typeof window !== 'undefined') {
    const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';
    if (!publishableKey) {
      console.error('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY não definido');
      return null;
    }
    stripePromise = loadStripe(publishableKey);
  }
  return stripePromise;
};

export const STRIPE_PRICES = {
  BASICO: 'price_1RtvdYLhhuHU7ecWyxiz6zti',
  PRO: 'price_1Rx7OBLhhuHU7ecWhotGOhi7',
  PREMIUM: 'price_1Rx7ZsLhhuHU7ecWtA3wXXXI'
};

export const redirectToCheckout = async (plan: 'BASICO' | 'PRO' | 'PREMIUM') => {
  try {
    if (typeof window === 'undefined') return;
    
    const stripePromise = getStripe();
    if (!stripePromise) return;
    
    const stripe = await stripePromise;
    if (!stripe) {
      console.error('Stripe não foi carregado');
      return;
    }

    const priceId = STRIPE_PRICES[plan];
    if (!priceId) {
      console.error('Plano inválido:', plan);
      return;
    }

    const successUrl = process.env.NEXT_PUBLIC_CHECKOUT_SUCCESS_URL || `${window.location.origin}/success`;
    const cancelUrl = process.env.NEXT_PUBLIC_CHECKOUT_CANCEL_URL || `${window.location.origin}/cancel`;

    const { error } = await stripe.redirectToCheckout({
      lineItems: [{
        price: priceId,
        quantity: 1,
      }],
      mode: 'subscription',
      successUrl,
      cancelUrl,
    });

    if (error) {
      console.error('Erro no checkout:', error);
    }
  } catch (err) {
    console.error('Erro ao processar plano:', err);
  }
};